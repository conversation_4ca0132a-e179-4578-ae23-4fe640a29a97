import { useEffect, useState, useCallback, useRef } from 'react';
import socketService from '@/lib/socketService';
import encryptionService from '@/lib/encryptionService';
import { publicKeyAPI } from '@/lib/publicKeyApi';

interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  status: 'sent' | 'delivered' | 'read';
  encrypted?: boolean;
  encryptedData?: {
    encryptedContent: string;
    encryptedKey: string;
    iv: string;
    algorithm: 'hybrid';
  };
}

interface UseEncryptedSocketChatProps {
  userId: string;
  chatId: string;
  onNewMessage?: (message: Message) => void;
  onMessageStatusUpdate?: (data: { messageId: string; status: string; isRead: boolean }) => void;
  onTypingUpdate?: (data: { userId: string; isTyping: boolean }) => void;
}

export const useEncryptedSocketChat = ({
  userId,
  chatId,
  onNewMessage,
  onMessageStatusUpdate,
  onTypingUpdate,
}: UseEncryptedSocketChatProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [isEncryptionReady, setIsEncryptionReady] = useState(false);
  const [encryptionError, setEncryptionError] = useState<string | null>(null);
  const hasJoinedRoom = useRef(false);
  const publicKeyCache = useRef<Map<string, string>>(new Map());

  // Initialize encryption
  const initializeEncryption = useCallback(async () => {
    try {
      console.log('🔐 Initializing encryption...');
      
      if (!encryptionService.isWebCryptoAvailable()) {
        throw new Error('Web Crypto API not available');
      }

      const userPublicKey = await encryptionService.initializeEncryption(userId);
      await publicKeyAPI.storePublicKey(userId, userPublicKey);
      
      setIsEncryptionReady(true);
      setEncryptionError(null);
      console.log('✅ Encryption initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize encryption:', error);
      setEncryptionError(error.message);
      setIsEncryptionReady(false);
    }
  }, [userId]);

  // Get recipient's public key with caching
  const getRecipientPublicKey = useCallback(async (recipientId: string): Promise<string | null> => {
    try {
      // Check cache first
      const cached = publicKeyCache.current.get(recipientId);
      if (cached) {
        return cached;
      }

      // Fetch from database
      const publicKey = await publicKeyAPI.getPublicKey(recipientId);
      if (publicKey) {
        publicKeyCache.current.set(recipientId, publicKey);
        encryptionService.cachePublicKey(recipientId, publicKey);
      }
      
      return publicKey;
    } catch (error) {
      console.error('❌ Failed to get recipient public key:', error);
      return null;
    }
  }, []);

  // Process incoming message (decrypt if encrypted)
  const processIncomingMessage = useCallback(async (message: Message): Promise<Message> => {
    try {
      if (message.encrypted && message.encryptedData) {
        console.log('🔓 Decrypting incoming message...');
        
        const decryptedContent = await encryptionService.decryptMessage(message.encryptedData);
        
        return {
          ...message,
          content: decryptedContent,
          encrypted: true, // Keep flag for UI indication
        };
      }
      
      return message;
    } catch (error) {
      console.error('❌ Failed to decrypt message:', error);
      return {
        ...message,
        content: '[🔒 Failed to decrypt message]',
        encrypted: true,
      };
    }
  }, []);

  // Send encrypted message
  const sendEncryptedMessage = useCallback(async (message: Omit<Message, 'id' | 'created_at' | 'status' | 'encrypted' | 'encryptedData'>) => {
    try {
      if (!isEncryptionReady) {
        throw new Error('Encryption not ready');
      }

      const recipientPublicKey = await getRecipientPublicKey(message.receiver_id);
      
      if (!recipientPublicKey) {
        console.log('⚠️ No public key found, sending unencrypted message');
        // Fall back to unencrypted message
        return sendUnencryptedMessage(message);
      }

      console.log('🔐 Encrypting message...');
      const encryptedData = await encryptionService.encryptMessage(message.content, recipientPublicKey);

      const fullMessage: Message = {
        ...message,
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString(),
        status: 'sent',
        encrypted: true,
        encryptedData,
      };

      console.log('📤 Sending encrypted message via Socket.IO:', fullMessage.id);
      socketService.sendMessage(fullMessage);
      return fullMessage;
    } catch (error) {
      console.error('❌ Failed to send encrypted message:', error);
      throw error;
    }
  }, [isEncryptionReady, getRecipientPublicKey]);

  // Send unencrypted message (fallback)
  const sendUnencryptedMessage = useCallback((message: Omit<Message, 'id' | 'created_at' | 'status'>) => {
    const fullMessage: Message = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString(),
      status: 'sent',
      encrypted: false,
    };

    console.log('📤 Sending unencrypted message via Socket.IO:', fullMessage.id);
    socketService.sendMessage(fullMessage);
    return fullMessage;
  }, []);

  // Join chat room when component mounts and socket connects
  useEffect(() => {
    if (userId && chatId && isConnected && !hasJoinedRoom.current) {
      console.log(`🏠 Joining chat room: ${chatId}`);
      socketService.joinChat(userId, chatId);
      hasJoinedRoom.current = true;
    }

    return () => {
      if (chatId && hasJoinedRoom.current) {
        console.log(`🚪 Leaving chat room: ${chatId}`);
        socketService.leaveChat(chatId);
        hasJoinedRoom.current = false;
      }
    };
  }, [userId, chatId, isConnected]);

  // Set up message listener with decryption
  useEffect(() => {
    if (!onNewMessage) return;

    const unsubscribe = socketService.onMessage(async (message: Message) => {
      const messageChatId = [message.sender_id, message.receiver_id].sort().join('-');
      if (messageChatId === chatId) {
        console.log('📨 Received message for this chat:', message.id);
        
        // Process (decrypt if needed) and forward to parent
        const processedMessage = await processIncomingMessage(message);
        onNewMessage(processedMessage);
      }
    });

    return unsubscribe;
  }, [chatId, onNewMessage, processIncomingMessage]);

  // Set up other listeners (unchanged)
  useEffect(() => {
    if (!onMessageStatusUpdate) return;
    const unsubscribe = socketService.onStatusUpdate(onMessageStatusUpdate);
    return unsubscribe;
  }, [onMessageStatusUpdate]);

  useEffect(() => {
    if (!onTypingUpdate) return;
    const unsubscribe = socketService.onTyping(onTypingUpdate);
    return unsubscribe;
  }, [onTypingUpdate]);

  // Set up connection status listener
  useEffect(() => {
    const unsubscribe = socketService.onConnectionChange((connected) => {
      console.log('🔗 Connection status changed:', connected);
      setIsConnected(connected);
      setConnectionStatus(connected ? 'connected' : 'disconnected');
      
      if (!connected) {
        hasJoinedRoom.current = false;
      }
    });

    const initiallyConnected = socketService.isSocketConnected();
    setIsConnected(initiallyConnected);
    setConnectionStatus(initiallyConnected ? 'connected' : 'connecting');

    return unsubscribe;
  }, []);

  // Initialize encryption when component mounts
  useEffect(() => {
    if (userId) {
      initializeEncryption();
    }
  }, [userId, initializeEncryption]);

  // Typing status functions (unchanged)
  const sendTypingStatus = useCallback((isTyping: boolean) => {
    try {
      socketService.sendTypingStatus({
        userId,
        chatId,
        isTyping,
      });
    } catch (error) {
      console.error('❌ Failed to send typing status:', error);
    }
  }, [userId, chatId]);

  const markAsDelivered = useCallback((messageId: string) => {
    try {
      socketService.updateMessageStatus({
        messageId,
        status: 'delivered',
        isRead: false,
      });
    } catch (error) {
      console.error('❌ Failed to mark as delivered:', error);
    }
  }, []);

  const markAsRead = useCallback((messageId: string) => {
    try {
      socketService.updateMessageStatus({
        messageId,
        status: 'read',
        isRead: true,
      });
    } catch (error) {
      console.error('❌ Failed to mark as read:', error);
    }
  }, []);

  return {
    isConnected,
    connectionStatus,
    isEncryptionReady,
    encryptionError,
    sendMessage: sendEncryptedMessage,
    sendUnencryptedMessage,
    sendTypingStatus,
    markAsDelivered,
    markAsRead,
    socketId: socketService.getSocketId(),
  };
};

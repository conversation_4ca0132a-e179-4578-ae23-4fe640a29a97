{"name": "klicktape-socket-server", "version": "1.0.0", "description": "Socket.IO server for Klicktape real-time chat", "main": "socket-server.js", "scripts": {"start": "node socket-server.js", "dev": "nodemon socket-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["socket.io", "real-time", "chat", "websocket"], "author": "Klicktape", "license": "MIT"}
import { supabase } from './supabase';

export const publicKeyAPI = {
  // Store user's public key in database
  storePublicKey: async (userId: string, publicKey: string) => {
    try {
      const { data, error } = await supabase
        .from('public_keys')
        .upsert(
          {
            user_id: userId,
            public_key: publicKey,
            created_at: new Date().toISOString(),
          },
          { onConflict: ['user_id'] }
        )
        .select()
        .single();

      if (error) throw error;
      console.log('✅ Public key stored in database');
      return data;
    } catch (error) {
      console.error('❌ Error storing public key:', error);
      throw new Error(`Failed to store public key: ${(error as Error).message}`);
    }
  },

  // Get recipient's public key
  getPublicKey: async (userId: string): Promise<string | null> => {
    try {
      const { data, error } = await supabase
        .from('public_keys')
        .select('public_key')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No public key found
          console.log('⚠️ No public key found for user:', userId);
          return null;
        }
        throw error;
      }

      console.log('✅ Public key retrieved for user:', userId);
      return data.public_key;
    } catch (error) {
      console.error('❌ Error getting public key:', error);
      throw new Error(`Failed to get public key: ${(error as Error).message}`);
    }
  },

  // Get multiple public keys at once
  getMultiplePublicKeys: async (userIds: string[]): Promise<Record<string, string>> => {
    try {
      const { data, error } = await supabase
        .from('public_keys')
        .select('user_id, public_key')
        .in('user_id', userIds);

      if (error) throw error;

      const keyMap: Record<string, string> = {};
      data.forEach(item => {
        keyMap[item.user_id] = item.public_key;
      });

      console.log('✅ Retrieved public keys for', data.length, 'users');
      return keyMap;
    } catch (error) {
      console.error('❌ Error getting multiple public keys:', error);
      throw new Error(`Failed to get public keys: ${(error as Error).message}`);
    }
  },

  // Delete user's public key (for logout/account deletion)
  deletePublicKey: async (userId: string) => {
    try {
      const { error } = await supabase
        .from('public_keys')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;
      console.log('✅ Public key deleted for user:', userId);
    } catch (error) {
      console.error('❌ Error deleting public key:', error);
      throw new Error(`Failed to delete public key: ${(error as Error).message}`);
    }
  },

  // Check if user has a public key
  hasPublicKey: async (userId: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase
        .from('public_keys')
        .select('user_id')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return !!data;
    } catch (error) {
      console.error('❌ Error checking public key existence:', error);
      return false;
    }
  },
};

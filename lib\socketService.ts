// Import polyfill for React Native
import 'react-native-get-random-values';
import { io, Socket } from 'socket.io-client';
import { Platform } from 'react-native';

interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  status: 'sent' | 'delivered' | 'read';
}

interface TypingData {
  userId: string;
  chatId: string;
  isTyping: boolean;
}

interface MessageStatusUpdate {
  messageId: string;
  status: 'sent' | 'delivered' | 'read';
  isRead: boolean;
}

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;

  // Event listeners
  private messageListeners: ((message: Message) => void)[] = [];
  private typingListeners: ((data: TypingData) => void)[] = [];
  private statusListeners: ((data: MessageStatusUpdate) => void)[] = [];
  private connectionListeners: ((connected: boolean) => void)[] = [];

  constructor() {
    this.connect();
  }

  private connect() {
    try {
      // Platform-specific server URL
      const getServerUrl = () => {
        // You can also use environment variables for different environments
        const SERVER_URL = process.env.EXPO_PUBLIC_SOCKET_SERVER_URL;

        if (SERVER_URL) {
          console.log('🌐 Using custom server URL from env:', SERVER_URL);
          return SERVER_URL;
        }

        if (Platform.OS === 'android') {
          // Android emulator uses ******** to access host machine
          // For physical device, use your computer's IP address
          return 'http://********:3000';
        } else if (Platform.OS === 'ios') {
          // iOS simulator can use localhost
          return 'http://localhost:3000';
        } else {
          // Web or other platforms
          return 'http://localhost:3000';
        }
      };

      const serverUrl = getServerUrl();
      console.log(`🔗 Connecting to Socket.IO server: ${serverUrl}`);

      // React Native compatible Socket.IO configuration
      this.socket = io(serverUrl, {
        transports: ['polling', 'websocket'], // Allow both transports
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 10, // Increase retry attempts
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        forceNew: false,
        upgrade: true, // Allow transport upgrades
        rememberUpgrade: false, // Don't remember upgrade for React Native
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Socket connection error:', error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Socket.IO connected:', this.socket?.id);
      this.isConnected = true;
      this.notifyConnectionListeners(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket.IO disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionListeners(false);
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.isConnected = false;
      this.notifyConnectionListeners(false);

      // Log specific error details for debugging
      if (error.message) {
        console.error('Error message:', error.message);
      }
      if (error.description) {
        console.error('Error description:', error.description);
      }
      if (error.context) {
        console.error('Error context:', error.context);
      }
    });

    // Listen for new messages
    this.socket.on('new_message', (message: Message) => {
      console.log('📨 New message received via Socket.IO:', message.id);
      this.notifyMessageListeners(message);
    });

    // Listen for message status updates
    this.socket.on('message_status_update', (data: MessageStatusUpdate) => {
      console.log('📊 Message status update:', data);
      this.notifyStatusListeners(data);
    });

    // Listen for typing indicators
    this.socket.on('typing_update', (data: TypingData) => {
      console.log('⌨️ Typing update:', data);
      this.notifyTypingListeners(data);
    });
  }

  // Join a chat room
  joinChat(userId: string, chatId: string) {
    if (this.socket && this.isConnected) {
      console.log(`🏠 Joining chat room: ${chatId}`);
      this.socket.emit('join_chat', { userId, chatId });
    } else {
      console.log('⏳ Socket not connected, will join chat when connected');
      // Retry when connected
      setTimeout(() => {
        if (this.socket && this.isConnected) {
          this.joinChat(userId, chatId);
        }
      }, 1000);
    }
  }

  // Leave a chat room
  leaveChat(chatId: string) {
    if (this.socket && this.isConnected) {
      console.log(`🚪 Leaving chat room: ${chatId}`);
      this.socket.emit('leave_chat', { chatId });
    }
  }

  // Send a message
  sendMessage(message: Message) {
    if (this.socket && this.isConnected) {
      console.log('📤 Sending message via Socket.IO:', message.id);
      this.socket.emit('send_message', message);
    } else {
      console.error('❌ Cannot send message: Socket not connected');
      throw new Error('Socket not connected');
    }
  }

  // Send typing status
  sendTypingStatus(data: TypingData) {
    if (this.socket && this.isConnected) {
      this.socket.emit('typing_status', data);
    }
  }

  // Update message status (delivered/read)
  updateMessageStatus(data: MessageStatusUpdate) {
    if (this.socket && this.isConnected) {
      this.socket.emit('message_status', data);
    }
  }

  // Event listener management
  onMessage(callback: (message: Message) => void) {
    this.messageListeners.push(callback);
    return () => {
      this.messageListeners = this.messageListeners.filter(cb => cb !== callback);
    };
  }

  onTyping(callback: (data: TypingData) => void) {
    this.typingListeners.push(callback);
    return () => {
      this.typingListeners = this.typingListeners.filter(cb => cb !== callback);
    };
  }

  onStatusUpdate(callback: (data: MessageStatusUpdate) => void) {
    this.statusListeners.push(callback);
    return () => {
      this.statusListeners = this.statusListeners.filter(cb => cb !== callback);
    };
  }

  onConnectionChange(callback: (connected: boolean) => void) {
    this.connectionListeners.push(callback);
    return () => {
      this.connectionListeners = this.connectionListeners.filter(cb => cb !== callback);
    };
  }

  // Notify listeners
  private notifyMessageListeners(message: Message) {
    this.messageListeners.forEach(callback => {
      try {
        callback(message);
      } catch (error) {
        console.error('Error in message listener:', error);
      }
    });
  }

  private notifyTypingListeners(data: TypingData) {
    this.typingListeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in typing listener:', error);
      }
    });
  }

  private notifyStatusListeners(data: MessageStatusUpdate) {
    this.statusListeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in status listener:', error);
      }
    });
  }

  private notifyConnectionListeners(connected: boolean) {
    this.connectionListeners.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error('Error in connection listener:', error);
      }
    });
  }

  // Utility methods
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  getSocketId(): string | undefined {
    return this.socket?.id;
  }

  // Manual reconnection
  reconnect() {
    console.log('🔄 Manual reconnection triggered');
    if (this.socket) {
      this.socket.disconnect();
    }
    this.isConnected = false;
    this.connect();
  }

  // Force connection check
  checkConnection() {
    if (this.socket) {
      console.log('🔍 Connection check - Socket connected:', this.socket.connected);
      console.log('🔍 Connection check - Internal connected:', this.isConnected);
      return this.socket.connected;
    }
    return false;
  }

  // Cleanup
  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting Socket.IO');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;

import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

interface KeyPair {
  publicKey: string;
  privateKey: string;
}

interface EncryptedMessage {
  encryptedContent: string;
  encryptedKey: string;
  iv: string;
  algorithm: 'hybrid'; // RSA + AES hybrid encryption
}

class EncryptionService {
  private static instance: EncryptionService;
  private userKeyPair: KeyPair | null = null;
  private publicKeyCache: Map<string, string> = new Map();

  static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  // Generate RSA key pair using Web Crypto API
  async generateKeyPair(): Promise<KeyPair> {
    try {
      console.log('🔐 Generating RSA-OAEP key pair...');
      
      const keyPair = await crypto.subtle.generateKey(
        {
          name: 'RSA-OAEP',
          modulusLength: 2048,
          publicExponent: new Uint8Array([1, 0, 1]),
          hash: 'SHA-256',
        },
        true, // extractable
        ['encrypt', 'decrypt']
      );

      // Export keys to PEM format
      const publicKeyBuffer = await crypto.subtle.exportKey('spki', keyPair.publicKey);
      const privateKeyBuffer = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);

      const keys: KeyPair = {
        publicKey: this.arrayBufferToBase64(publicKeyBuffer),
        privateKey: this.arrayBufferToBase64(privateKeyBuffer),
      };

      console.log('✅ RSA key pair generated successfully');
      return keys;
    } catch (error) {
      console.error('❌ Error generating key pair:', error);
      throw new Error('Failed to generate encryption keys');
    }
  }

  // Hybrid encryption: AES for content + RSA for AES key
  async encryptMessage(message: string, recipientPublicKey: string): Promise<EncryptedMessage> {
    try {
      console.log('🔐 Encrypting message with hybrid encryption...');

      // Step 1: Generate AES-GCM key for this message
      const aesKey = await crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );

      // Step 2: Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(12));

      // Step 3: Encrypt message with AES-GCM
      const messageBuffer = new TextEncoder().encode(message);
      const encryptedMessageBuffer = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        aesKey,
        messageBuffer
      );

      // Step 4: Export AES key as raw bytes
      const aesKeyBuffer = await crypto.subtle.exportKey('raw', aesKey);

      // Step 5: Import recipient's RSA public key
      const publicKeyBuffer = this.base64ToArrayBuffer(recipientPublicKey);
      const importedPublicKey = await crypto.subtle.importKey(
        'spki',
        publicKeyBuffer,
        { name: 'RSA-OAEP', hash: 'SHA-256' },
        false,
        ['encrypt']
      );

      // Step 6: Encrypt AES key with RSA public key
      const encryptedKeyBuffer = await crypto.subtle.encrypt(
        { name: 'RSA-OAEP' },
        importedPublicKey,
        aesKeyBuffer
      );

      const result: EncryptedMessage = {
        encryptedContent: this.arrayBufferToBase64(encryptedMessageBuffer),
        encryptedKey: this.arrayBufferToBase64(encryptedKeyBuffer),
        iv: this.arrayBufferToBase64(iv),
        algorithm: 'hybrid',
      };

      console.log('✅ Message encrypted successfully');
      return result;
    } catch (error) {
      console.error('❌ Error encrypting message:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  // Decrypt hybrid encrypted message
  async decryptMessage(encryptedMessage: EncryptedMessage): Promise<string> {
    try {
      console.log('🔓 Decrypting message...');

      if (!this.userKeyPair) {
        throw new Error('User keys not loaded');
      }

      // Step 1: Import user's private key
      const privateKeyBuffer = this.base64ToArrayBuffer(this.userKeyPair.privateKey);
      const importedPrivateKey = await crypto.subtle.importKey(
        'pkcs8',
        privateKeyBuffer,
        { name: 'RSA-OAEP', hash: 'SHA-256' },
        false,
        ['decrypt']
      );

      // Step 2: Decrypt AES key with RSA private key
      const encryptedKeyBuffer = this.base64ToArrayBuffer(encryptedMessage.encryptedKey);
      const aesKeyBuffer = await crypto.subtle.decrypt(
        { name: 'RSA-OAEP' },
        importedPrivateKey,
        encryptedKeyBuffer
      );

      // Step 3: Import AES key
      const aesKey = await crypto.subtle.importKey(
        'raw',
        aesKeyBuffer,
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      // Step 4: Decrypt message with AES-GCM
      const iv = this.base64ToArrayBuffer(encryptedMessage.iv);
      const encryptedContentBuffer = this.base64ToArrayBuffer(encryptedMessage.encryptedContent);
      
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        aesKey,
        encryptedContentBuffer
      );

      const decryptedMessage = new TextDecoder().decode(decryptedBuffer);
      console.log('✅ Message decrypted successfully');
      return decryptedMessage;
    } catch (error) {
      console.error('❌ Error decrypting message:', error);
      return '[🔒 Failed to decrypt message]';
    }
  }

  // Secure key storage with Expo SecureStore
  async storeKeyPair(keyPair: KeyPair, userId: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(`private_key_${userId}`, keyPair.privateKey);
      await SecureStore.setItemAsync(`public_key_${userId}`, keyPair.publicKey);
      this.userKeyPair = keyPair;
      console.log('✅ Keys stored securely');
    } catch (error) {
      console.error('❌ Error storing keys:', error);
      throw new Error('Failed to store encryption keys');
    }
  }

  // Load keys from secure storage
  async loadKeyPair(userId: string): Promise<KeyPair | null> {
    try {
      const privateKey = await SecureStore.getItemAsync(`private_key_${userId}`);
      const publicKey = await SecureStore.getItemAsync(`public_key_${userId}`);

      if (privateKey && publicKey) {
        this.userKeyPair = { privateKey, publicKey };
        console.log('✅ Keys loaded from secure storage');
        return this.userKeyPair;
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading keys:', error);
      return null;
    }
  }

  // Initialize encryption for user
  async initializeEncryption(userId: string): Promise<string> {
    try {
      // Try to load existing keys
      let keyPair = await this.loadKeyPair(userId);
      
      if (!keyPair) {
        console.log('🔐 No existing keys found, generating new ones...');
        keyPair = await this.generateKeyPair();
        await this.storeKeyPair(keyPair, userId);
      }

      return keyPair.publicKey;
    } catch (error) {
      console.error('❌ Error initializing encryption:', error);
      throw new Error('Failed to initialize encryption');
    }
  }

  // Get user's public key
  getUserPublicKey(): string | null {
    return this.userKeyPair?.publicKey || null;
  }

  // Cache public keys for performance
  cachePublicKey(userId: string, publicKey: string): void {
    this.publicKeyCache.set(userId, publicKey);
  }

  getCachedPublicKey(userId: string): string | null {
    return this.publicKeyCache.get(userId) || null;
  }

  // Clear keys (for logout)
  async clearKeys(userId: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(`private_key_${userId}`);
      await SecureStore.deleteItemAsync(`public_key_${userId}`);
      this.userKeyPair = null;
      this.publicKeyCache.clear();
      console.log('✅ Keys cleared');
    } catch (error) {
      console.error('❌ Error clearing keys:', error);
    }
  }

  // Utility functions
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  // Check if Web Crypto API is available
  isWebCryptoAvailable(): boolean {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined' &&
           typeof crypto.getRandomValues !== 'undefined';
  }
}

export default EncryptionService.getInstance();
